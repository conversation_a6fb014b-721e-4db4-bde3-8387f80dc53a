package com.ainemo.iauth.web.api.rest.resource.approval;

import com.ainemo.iauth.rest.dto.UnlockBlockAccountDTO;
import com.ainemo.iauth.rest.vo.ApprovalPwdVO;
import com.ainemo.iauth.rest.vo.BlockAccountUnlockVO;
import com.ainemo.iauth.rest.vo.BlockAccountVO;
import com.ainemo.iauth.rest.vo.FailedVO;
import com.ainemo.iauth.service.BlockAccountService;
import com.ainemo.iauth.service.UserOperationApprovalService;
import com.ainemo.iauth.web.api.rest.RestException;
import com.ainemo.iauth.web.api.rest.resource.info.PasswordResetApprovalRequest;
import com.ainemo.libra.shared.enums.ErrorStatus;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.List;

@Component
@Scope("prototype")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Path("/api/rest/iauth/internal/approval")
@Slf4j
public class ApprovalResource {

    @Autowired
    private UserOperationApprovalService userOperationApprovalService;
    @Autowired
    private BlockAccountService blockAccountService;

    @Path("/user/password/reset/v1")
    @Produces({MediaType.TEXT_PLAIN, MediaType.APPLICATION_JSON})
    @POST
    public ApprovalPwdVO passwordResetApproval(PasswordResetApprovalRequest request) {
        log.info("approval password reset: {}", request);
        List<String> successList = new ArrayList<>();
        List<FailedVO> failedList = new ArrayList<>();
        for (String id : request.getIdList()) {
            try {
                successList.add(userOperationApprovalService.approvalPasswordReset(id));
            } catch (Exception e) {
                int errorCode;
                String errorMessage;
                if (e instanceof RestException) {
                    RestException e1 = (RestException) e;
                    errorCode = e1.getMsg().getErrorCode();
                    errorMessage = e1.getMsg().getUserMessage();
                } else {
                    errorCode = ErrorStatus.INTERNAL_ERROR.getErrorCode();
                    errorMessage = ErrorStatus.INTERNAL_ERROR.getResId();
                }
                failedList.add(new FailedVO(errorCode, errorMessage, id));
            }
        }
        return new ApprovalPwdVO(successList, failedList);
    }

    @Path("/user/block/page/v1")
    @Produces({MediaType.TEXT_PLAIN, MediaType.APPLICATION_JSON})
    @GET
    public IPage<BlockAccountVO> getBlockAccount(@QueryParam("page") int page,
                                                 @QueryParam("size") int size,
                                                 @QueryParam("params") String params,
                                                 @QueryParam("roleNameList") @DefaultValue("") String roleNameListStr) {
        return blockAccountService.getByPage(page, size, params, roleNameListStr);
    }

    @Path("/user/block/roleName/list/v1")
    @Produces({MediaType.TEXT_PLAIN, MediaType.APPLICATION_JSON})
    @GET
    public List<String> getRoleList() {
        return blockAccountService.getRoleList();
    }

    @Path("user/block/unlock/v1")
    @Produces({MediaType.TEXT_PLAIN, MediaType.APPLICATION_JSON})
    @POST
    public BlockAccountUnlockVO unlockBlockAccount(UnlockBlockAccountDTO dto) {
        log.info("unlock dto: {}", dto);
        return blockAccountService.unlockBlockAccount(dto.getUnlockList());
    }
}
