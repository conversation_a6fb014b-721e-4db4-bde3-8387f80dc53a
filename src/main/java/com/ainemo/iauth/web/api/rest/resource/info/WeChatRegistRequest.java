package com.ainemo.iauth.web.api.rest.resource.info;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/9/7.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class WeChatRegistRequest {

  private String openId;
  private String phone;
  private String adminName;
  private String countryCode;
  private String password;
  private String verificationCode;

  public String getOpenId() {
    return openId;
  }

  public void setOpenId(String openId) {
    this.openId = openId;
  }

  public String getPhone() {
    return phone;
  }

  public void setPhone(String phone) {
    this.phone = phone;
  }

  public String getAdminName() {
    return adminName;
  }

  public void setAdminName(String adminName) {
    this.adminName = adminName;
  }

  public String getCountryCode() {
    return countryCode;
  }

  public void setCountryCode(String countryCode) {
    this.countryCode = countryCode;
  }

  public String getPassword() {
    return password;
  }

  public void setPassword(String password) {
    this.password = password;
  }

  public String getVerificationCode() {
    return verificationCode;
  }

  public void setVerificationCode(String verificationCode) {
    this.verificationCode = verificationCode;
  }

  @Override
  public String toString() {
    return "WeChatRegistRequest{" +
        "openId='" + openId + '\'' +
        ", phone='" + phone + '\'' +
        ", adminName='" + adminName + '\'' +
        ", countryCode='" + countryCode + '\'' +
        '}';
  }
}
