package com.ainemo.iauth.web.api.rest.resource.info;

import com.ainemo.libra.web.api.rest.data.RegistrationRequest;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2019-06-21 17:57
 */
public class InternalRegistrationRequest extends RegistrationRequest implements Serializable {

    private static final long serialVersionUID = 6653716716697001318L;
    private Boolean sendPwdSms;
    private Integer hasActivityQualification;


    public Boolean getSendPwdSms() {
        return sendPwdSms;
    }

    public void setSendPwdSms(Boolean sendPwdSms) {
        this.sendPwdSms = sendPwdSms;
    }

    public Integer getHasActivityQualification() {
        return hasActivityQualification;
    }

    public void setHasActivityQualification(Integer hasActivityQualification) {
        this.hasActivityQualification = hasActivityQualification;
    }

    @Override
    public String toString() {
        return "InternalRegistrationRequest{" +
                "sendPwdSms=" + sendPwdSms +
                ", hasActivityQualification=" + hasActivityQualification +
                "} " + super.toString();
    }
}
