package com.ainemo.libra.infrastructure.statistics.impl;

import org.apache.log4j.Logger;

import com.ainemo.libra.infrastructure.mq.MessageQueue;
import com.ainemo.libra.infrastructure.statistics.StatisticsCollector;

public class StatisticsCollectorUsingMq implements StatisticsCollector {
	private static final String START_CALL_LOG_TEMPLATE = "{\"collection\":\"log\",\"content\":{\"type\":\"start_call\",\"callid\":\"%s\",\"from\":\"%s\",\"to\":\"%s\",\"timestamp\":%s}}";
	private static final String END_CALL_LOG_TEMPLATE    = "{\"collection\":\"log\",\"content\":{\"type\":\"end_call\",\"callid\":\"%s\",\"timestamp\":%s}}";
	
//	private static final String EVENT_TEMPLATE = "{\"collection\":\"eventreport\",\"content\":%s}";

	private final MessageQueue messageQueue;
	private final Logger logger = Logger
			.getLogger(StatisticsCollectorUsingMq.class);

	private void innnerSendText(String text) {
		try {
			if(text == null){
				logger.error("Fail to sent null " , new Throwable());
			}
			messageQueue.sendText(text);

			if (logger.isDebugEnabled()) {
				logger.debug("Successfully sent " + text);
			}
		} catch (Exception ignore) {
			if (logger.isDebugEnabled()) {
				logger.debug("Fail to send " + text, ignore);
			}
		}
	}

	public StatisticsCollectorUsingMq(MessageQueue messageQueue) {
		this.messageQueue = messageQueue;
	}

	@Override
	public void collectStartCall(String callId, String fromUser, String toUser,
			long timestamp) {
		String msg = String.format(START_CALL_LOG_TEMPLATE, callId, fromUser,
				toUser, timestamp);
		innnerSendText(msg);
	}

	@Override
	public void collectEndCall(String callId, long timestamp) {
		String msg = String.format(END_CALL_LOG_TEMPLATE, callId, timestamp);
		innnerSendText(msg);
	}

	@Override
	public void collectEvent(String eventInJsonFormat) {
		innnerSendText(eventInJsonFormat);
	}
}
