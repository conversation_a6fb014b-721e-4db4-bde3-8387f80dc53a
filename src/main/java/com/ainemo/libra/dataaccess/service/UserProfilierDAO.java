package com.ainemo.libra.dataaccess.service;

import com.ainemo.libra.dataaccess.DataAccessException;
import com.ainemo.libra.dataaccess.po.UserProfilePO;
import com.ainemo.libra.domain.Mobile;

import java.util.List;

public interface UserProfilierDAO extends CoreUserProfileDAO {
	UserProfilePO add(UserProfilePO user) throws DataAccessException;
	
	List<UserProfilePO> getUserProfileByMobile(Mobile mobile);
	
	UserProfilePO getUserProfilePOByIdentityKey(String identityKey);
	
	boolean isUserIdentiyExists(String identity);
	
	List<UserProfilePO> getListByIds(List<Long> ids) throws DataAccessException ;

	boolean mailExist(String mail);

	UserProfilePO getUserProfileByEmail(String email);

	List<UserProfilePO> getUserProfileByEnterpriseId(String enterpriseId);
	List<UserProfilePO> getValidUserProfileByEnterpriseId(String enterpriseId);
	List<Long> getUserIdByEnterpriseId(String enterpriseId);

	UserProfilePO getUserProfileByCallNumber(String callNumber);

	List<UserProfilePO> getChinaUserProfileByPhonesAndType(List<String> userPhone, int type);
	UserProfilePO getValidUserProfileByEnterpriseId(Long userId);

	/**
	 * 批量查询(不带国家码)
	 */
	List<UserProfilePO> getBatchUserProfileByMobile(List<String> phones);

	List<UserProfilePO> getBatchUserProfilePOById(List<Long> ids);

	UserProfilePO getUserProfileByEmailAllType(String email);

	UserProfilePO getUserProfileByUserPhone(String userPhone);

	UserProfilePO getUserProfileByPhone(String phone);

	int getUserCountByEnterpriseId(String enterpriseId);

}
