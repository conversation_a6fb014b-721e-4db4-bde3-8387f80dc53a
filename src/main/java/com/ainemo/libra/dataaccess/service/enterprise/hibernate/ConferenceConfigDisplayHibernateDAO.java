package com.ainemo.libra.dataaccess.service.enterprise.hibernate;

import com.ainemo.libra.dataaccess.po.enterprise.ConferenceConfigDisplayPO;
import com.ainemo.libra.dataaccess.po.enterprise.ConferenceManageConfigPO;
import com.ainemo.libra.dataaccess.service.enterprise.ConferenceConfigDisplayDAO;
import com.ainemo.libra.dataaccess.service.hibernate.StandardHibernateDAO;
import org.hibernate.Query;
import org.hibernate.SessionFactory;

import java.util.List;

/**
 * Created by wang<PERSON><PERSON> on 10/03/2018.
 */
public class ConferenceConfigDisplayHibernateDAO extends
        StandardHibernateDAO<ConferenceConfigDisplayPO> implements ConferenceConfigDisplayDAO {

    private final String TYPE_CONFERENCE = "conference";
    private final String TYPE_ENTERPRISE = "enterprise";
    private final String TYPE_DEFAULT = "default";
    public ConferenceConfigDisplayHibernateDAO(SessionFactory sessionFactory) {
        super(sessionFactory);
    }

    @Override
    public List<ConferenceConfigDisplayPO> getConferenceConfigDisplays(String number) {
        String hql = "from ConferenceConfigDisplayPO where configProfileType = '"+TYPE_CONFERENCE+"' and configProfileValue=:number";
        Query query = getSession().createQuery(hql).setString("number", number);
        return (List<ConferenceConfigDisplayPO>) query.list();
    }

    @Override
    public List<ConferenceConfigDisplayPO> getEnterpriseConfigDisplays(String enterpriseId) {
        String hql = "from ConferenceConfigDisplayPO where configProfileType = '"+TYPE_ENTERPRISE+"' and configProfileValue=:enterpriseId";
        Query query = getSession().createQuery(hql).setString("enterpriseId", enterpriseId);
        return (List<ConferenceConfigDisplayPO>) query.list();
    }

    @Override
    public List<ConferenceConfigDisplayPO> getDefaultEnterpriseConfigDisplays() {
        String hql = "from ConferenceConfigDisplayPO where configProfileType = '"+TYPE_ENTERPRISE+"' and configProfileValue='"+TYPE_DEFAULT+"'";
        Query query = getSession().createQuery(hql);
        return (List<ConferenceConfigDisplayPO>) query.list();
    }

    @Override
    public List<ConferenceConfigDisplayPO> getDefaultConfigDisplays() {
        String hql = "from ConferenceConfigDisplayPO where configProfileType = '"+TYPE_ENTERPRISE+"' and configProfileValue='"+TYPE_DEFAULT+"'"
                + "and configName='" + TYPE_DEFAULT + "'";
        Query query = getSession().createQuery(hql);
        return (List<ConferenceConfigDisplayPO>) query.list();
    }
}
