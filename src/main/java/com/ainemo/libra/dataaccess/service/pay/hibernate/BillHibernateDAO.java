package com.ainemo.libra.dataaccess.service.pay.hibernate;

import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Restrictions;

import com.ainemo.libra.dataaccess.po.pay.PayAccountPo;
import com.ainemo.libra.dataaccess.po.pay.PayBillPo;
import com.ainemo.libra.dataaccess.service.hibernate.StandardHibernateDAO;
import com.ainemo.libra.dataaccess.service.pay.PayBillDAO;

public class BillHibernateDAO extends StandardHibernateDAO<PayBillPo> implements PayBillDAO {

	public BillHibernateDAO(SessionFactory sessionFactory) {
		super(sessionFactory);
	}
	
	@Override
	public PayBillPo queryBillByTradeNo(String partnerId, String tradeNo) {
		
		Criteria criteria = getSession().createCriteria(PayBillPo.class);
		criteria.add(Restrictions.eq("partnerId", partnerId));
		criteria.add(Restrictions.eq("tradeNo", tradeNo));
		
		@SuppressWarnings("unchecked")
		List<PayBillPo> pos = (List<PayBillPo>) criteria.list();
		if(pos != null && pos.size() > 0){
			return pos.get(0);
		} else {
			return null;
		}
		
	}

}
