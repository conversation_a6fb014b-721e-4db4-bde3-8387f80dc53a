package com.ainemo.libra.dataaccess.service.hibernate.vod;

import com.ainemo.libra.dataaccess.po.vod.VodDownloadHistoryPO;
import com.ainemo.libra.dataaccess.po.vod.VodDownloadPO;
import com.ainemo.libra.dataaccess.service.vod.VodDownloadDAO;
import com.ainemo.libra.dataaccess.service2.hibernate.StandardHibernateDAO;
import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Restrictions;

/**
 * Created by wenya on 16/6/23.
 */
public class VodDownloadHibernateDAO extends StandardHibernateDAO<VodDownloadPO> implements VodDownloadDAO {

    public VodDownloadHibernateDAO(SessionFactory sessionFactory) {
        super(sessionFactory);
    }

    @Override
    public VodDownloadPO getByFavoriteId(long favoriteId) {
        Criteria criteria = getSession().createCriteria(VodDownloadPO.class);
        criteria.add(Restrictions.eq("favoriteVodId", favoriteId));

        return (VodDownloadPO) criteria.uniqueResult();
    }

    @Override
    public void updateDownloadCount(long favoriteId) {
        String sql = "update libra_vod_download set total_count = (total_count + 1) where favorite_vod_id = :favoriteId";
        Query query = getSession().createSQLQuery(sql).setLong("favoriteId", favoriteId);
        query.executeUpdate();
    }

    @Override
    public void saveDownloadHistory(VodDownloadHistoryPO downloadHistoryPO) {
        getSession().saveOrUpdate(downloadHistoryPO);
    }

    @Override
    public VodDownloadHistoryPO getDownloadHistory(String downloadId) {
        String hql = "from VodDownloadHistoryPO where downloadId = :downloadId";
        Query query = getSession().createQuery(hql).setString("downloadId", downloadId);
        return (VodDownloadHistoryPO) query.uniqueResult();
    }

    @Override
    public int getVodDownloadCount(String downloadWeek, long favoriteId) {
        String hql = "select count(*) from VodDownloadHistoryPO where favoriteId = :favoriteId and downloadWeek = :downloadWeek";
        Query query = getSession().createQuery(hql)
                .setLong("favoriteId", favoriteId)
                .setString("downloadWeek", downloadWeek);
        return ((Long) query.uniqueResult()).intValue();
    }
}
