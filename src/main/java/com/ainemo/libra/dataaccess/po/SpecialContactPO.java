package com.ainemo.libra.dataaccess.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * Created by root on 2/18/16.
 */
@Entity
@Table(name = "libra_special_contact")
public class SpecialContactPO extends StandardPO {

    private static final long  serialVersionUID = -1;

    private String name;
    private String number;
    private int target;
    @Column(name = "contact_usage")
    private int usage;//voice contact or circle member
    @Column(name = "contact_type")
    private int contactType;//nemo number or user id
    @Column(name = "i18n_name")
    private String i18nName;
    @Transient
    private int contactStatus=1;

    public SpecialContactPO() {

    }

    public SpecialContactPO(String name, String number) {
        this.name = name;
        this.number = number;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public int getTarget() {
        return target;
    }

    public void setTarget(int target) {
        this.target = target;
    }

    public int getUsage() {
        return usage;
    }

    public void setUsage(int usage) {
        this.usage = usage;
    }

    public int getContactType() {
        return contactType;
    }

    public void setContactType(int contactType) {
        this.contactType = contactType;
    }

    public String getI18nName() {
        return i18nName;
    }

    public void setI18nName(String i18nName) {
        this.i18nName = i18nName;
    }

    public int getContactStatus() {
        return contactStatus;
    }

    public void setContactStatus(int contactStatus) {
        this.contactStatus = contactStatus;
    }
}
