package com.ainemo.libra.dataaccess.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since 2019-04-23 19:45
 */
@Entity
@Table(name = "libra_touch_nc20")
public class TouchNC20PO extends BasePO {

    private static final int DEFAULT_CYCLE = 365;

    private static final long serialVersionUID = -2431917970811492628L;
    @Column(name = "sn")
    private String sn;
    @Column(name = "cycle")
    private int cycle;
    @Column(name = "status")
    private int status;
    @Column(name = "create_time")
    private Long createTime;
    @Column(name = "machine_code")
    private String machineCode;
    @Column(name = "bind_time")
    private Long bindTime;

    public TouchNC20PO() {
    }

    public TouchNC20PO(String sn, int cycle) {
        this.sn = sn;
        this.cycle = cycle;
        this.status = TouchNC20StatusEnum.NORMAL.getCode();
        this.createTime = System.currentTimeMillis();
    }

    public TouchNC20PO(String sn) {
        this(sn, DEFAULT_CYCLE);
    }

    public boolean isAvailability() {
        return this.status == TouchNC20StatusEnum.NORMAL.getCode();
    }


    /**
     * 绑定
     *
     * @param machineCode
     */
    public void bindMachine(String machineCode) {
        this.status = TouchNC20StatusEnum.USED.getCode();
        this.machineCode = machineCode;
        this.bindTime = System.currentTimeMillis();
    }

    public enum TouchNC20StatusEnum {
        NORMAL(0), USED(1);
        private int code;

        TouchNC20StatusEnum(int code) {
            this.code = code;
        }

        public int getCode() {
            return code;
        }
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public int getCycle() {
        return cycle;
    }

    public void setCycle(int cycle) {
        this.cycle = cycle;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getMachineCode() {
        return machineCode;
    }

    public void setMachineCode(String machineCode) {
        this.machineCode = machineCode;
    }

    public Long getBindTime() {
        return bindTime;
    }

    public void setBindTime(Long bindTime) {
        this.bindTime = bindTime;
    }

    @Override
    public String toString() {
        return "TouchNC20PO{" +
                "sn='" + sn + '\'' +
                ", cycle=" + cycle +
                ", status=" + status +
                ", createTime=" + createTime +
                ", machineCode='" + machineCode + '\'' +
                ", bindTime=" + bindTime +
                '}';
    }
}
