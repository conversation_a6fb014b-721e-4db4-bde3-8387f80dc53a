package com.ainemo.libra.dataaccess.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 废弃小鱼号
 * 
 * <AUTHOR>
 *
 */
// @Audited
@Entity
@Table(name = "libra_abandoned_magic_number")
public class AbandonedMagicNumberPO extends StandardPO {

	private static final long serialVersionUID = 1L;
	@Column(name = "magic_number", unique = true)
	private String magicNumber;
	@Column(name = "last_abandoned_timestamp")
	private long lastAbandonedTimestamp;

	public String getMagicNumber() {
		return magicNumber;
	}

	public void setMagicNumber(String magicNumber) {
		this.magicNumber = magicNumber;
	}

	public long getLastAbandonedTimestamp() {
		return lastAbandonedTimestamp;
	}

	public void setLastAbandonedTimestamp(long lastAbandonedTimestamp) {
		this.lastAbandonedTimestamp = lastAbandonedTimestamp;
	}

	public AbandonedMagicNumberPO() {
		super();
	}

	public AbandonedMagicNumberPO(String magicNumber, long lastAbandonedTimestamp) {
		super();
		this.magicNumber = magicNumber;
		this.lastAbandonedTimestamp = lastAbandonedTimestamp;
	}

}
