package com.ainemo.libra.dataaccess.po.enterprise.contact;

import com.ainemo.libra.dataaccess.po.BasePO;
import com.ainemo.libra.dataaccess.po.UserProfileField;
import com.ainemo.libra.dataaccess.po.UserProfilePO;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by wenya on 17/7/12.
 */
@Entity
@Table(name = "libra_user_external")
public class UserProfileExtPO extends BasePO{

    public static final String FIELD_NAME_PINYIN = "nameCode";
    public static final String FIELD_NAME_TELPHONE = "telephone";
    public static final String FIELD_NAME_TITLE = "title";

    @Column(name = "name_code")
    private String nameCode;
    private String telephone;
    private String title;
    @Column(name = "email_activation")
    @Type(type = "numeric_boolean")
    private boolean emailActivation;
    @JoinColumn(name = "user_id")
    @ManyToOne(targetEntity = UserProfilePO.class, fetch = FetchType.LAZY)
    private UserProfilePO userProfilePO;
    @Column(name = "register_source")
    private String registerSource;
    /**
     * 身份证信息
     */
    @Column(name = "id_card")
    private String idCard;
    @Column(name = "expiration_time")
    private Long expirationTime;
    @Column(name = "applicant")
    private String applicant;
    @Column(name = "applicant_department")
    private String applicantDepartment;
    @Column(name = "temp_user")
    @Type(type = "numeric_boolean")
    private boolean tempUser;


    @Column(name = "init_password")
    private String initPassword;

    public UserProfileExtPO() {

    }

    public UserProfileExtPO(UserProfilePO userProfilePO, String nameCode) {
        this(userProfilePO, nameCode, null, null,null);
    }

//    public UserProfileExtPO(UserProfilePO userProfilePO, String nameCode, String telephone, String title) {
//        this.userProfilePO = userProfilePO;
//        this.nameCode = nameCode;
//        this.telephone = telephone;
//        this.title = title;
//    }

    public UserProfileExtPO(UserProfilePO userProfilePO, String nameCode, String telephone, String title,String registerSource) {
        this.userProfilePO = userProfilePO;
        this.nameCode = nameCode;
        this.telephone = telephone;
        this.title = title;
        this.registerSource = registerSource;
    }

    public UserProfileExtPO(UserProfilePO userProfilePO, String nameCode, String telephone, String title,String registerSource, String initPassword) {
        this.userProfilePO = userProfilePO;
        this.nameCode = nameCode;
        this.telephone = telephone;
        this.title = title;
        this.registerSource = registerSource;
        this.initPassword = initPassword;
    }

    public String getNameCode() {
        return nameCode;
    }

    public void setNameCode(String nameCode) {
        this.nameCode = nameCode;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public UserProfilePO getUserProfilePO() {
        return userProfilePO;
    }

    public void setUserProfilePO(UserProfilePO userProfilePO) {
        this.userProfilePO = userProfilePO;
    }

    public String getRegisterSource() {
        return registerSource;
    }

    public void setRegisterSource(String registerSource) {
        this.registerSource = registerSource;
    }

    public boolean isEmailActivation() {
        return emailActivation;
    }

    public void setEmailActivation(boolean emailActivation) {
        this.emailActivation = emailActivation;
    }

    public static boolean isValidValue(UserProfileField field) {
        return FIELD_NAME_PINYIN.equals(field.getName()) || FIELD_NAME_TELPHONE.equals(field.getName())
                || FIELD_NAME_TITLE.equals(field.getName());
    }

    public String getInitPassword() {
        return initPassword;
    }

    public void setInitPassword(String initPassword) {
        this.initPassword = initPassword;
    }

    @Override
    public String toString() {
        return "{\"UserProfileExtPO\":{"
                + "\"nameCode\":\""
                + nameCode + '\"'
                + ",\"telephone\":\""
                + telephone + '\"'
                + ",\"title\":\""
                + title + '\"'
                + ",\"emailActivation\":"
                + emailActivation
                + ",\"initPassword\":"
                + initPassword
                + ",\"userProfilePO\":"
                + userProfilePO
                + ",\"registerSource\":\""
                + registerSource + '\"'
                + "},\"super-UserProfileExtPO\":" + super.toString() + "}";
    }

    public Long getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(Long expirationTime) {
        this.expirationTime = expirationTime;
    }

    public String getApplicant() {
        return applicant;
    }

    public void setApplicant(String applicant) {
        this.applicant = applicant;
    }

    public String getApplicantDepartment() {
        return applicantDepartment;
    }

    public void setApplicantDepartment(String applicantDepartment) {
        this.applicantDepartment = applicantDepartment;
    }

    public boolean isTempUser() {
        return tempUser;
    }

    public void setTempUser(boolean tempUser) {
        this.tempUser = tempUser;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }
}
