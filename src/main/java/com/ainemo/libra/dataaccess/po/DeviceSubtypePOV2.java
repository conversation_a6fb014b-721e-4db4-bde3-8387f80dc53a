package com.ainemo.libra.dataaccess.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "libra_device_subtype_model")
public class DeviceSubtypePOV2 extends StandardPO {

    @Column(name = "sub_type")
    private int subType;

    @Column(name = "type")
    private int type;

    @Column(name = "category")
    private String category;

    @Column(name = "category_display")
    private String categoryDisplay;//展示类型

    @Column(name = "type_category")
    private String typeCategory;//大类型

    @Column(name = "is_charge_session")
    private boolean isChargeSession;//是否计方数

    @Column(name = "is_charge_port")
    private boolean isChargePort;//是否交端口使用费

    @Column(name = "is_present_session")
    private boolean isPresentSession;//是否赠送方数

    @Column(name = "is_join_enterprise")
    private boolean isJoinEnterprise;//是否可以加入企业

    @Column(name = "is_modify_manager")
    private boolean isModifyManager;//是否支持修改管理员

    @Column(name = "cart_vip_type")
    private String cartVipType;//延保卡号类型

    @Column(name = "cart_session_type")
    private String cartSessionType;//'端口使用费卡号类型',

    @Column(name = "is_adjuste_volume")
    private boolean isAdjustVolume;//'是否支持调节音量' ,

    @Column(name = "is_multi_image")
    private boolean isCanMultiImage;//'是否支持多画面' ,

    @Column(name = "multi_image")
    private String multiImage;//'多画面能力',

    @Column(name = "device_numer_prefix")
    private String deviceNumberPrefix;//设备号前缀',

    @Column(name = "is_associate")
    private  boolean isAssociate;//'是否可联接' ,

    @Column(name = "sn_prefix")
    private String snPrefix;// 'sn前缀'

    @Column(name = "is_touch_screen")
    private boolean isTouchScreen;// '是否触屏版'

    @Column(name = "is_third_device")
    private boolean isThirdDevice;// '是否第三方设备'

    @Column(name = "inspect")
    private boolean inspect;// '是否具有巡检能力'

    /*@Column(name = "is_bind_conference")
    private boolean isBindConference;// '是否绑定云会议室'*/


    public DeviceSubtypePOV2() {

    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCategoryDisplay() {
        return categoryDisplay;
    }

    public void setCategoryDisplay(String categoryDisplay) {
        this.categoryDisplay = categoryDisplay;
    }

    public String getTypeCategory() {
        return typeCategory;
    }

    public void setTypeCategory(String typeCategory) {
        this.typeCategory = typeCategory;
    }

    public boolean isChargeSession() {
        return isChargeSession;
    }

    public void setChargeSession(boolean chargeSession) {
        isChargeSession = chargeSession;
    }

    public boolean isChargePort() {
        return isChargePort;
    }

    public void setChargePort(boolean chargePort) {
        isChargePort = chargePort;
    }

    public boolean isPresentSession() {
        return isPresentSession;
    }

    public void setPresentSession(boolean presentSession) {
        isPresentSession = presentSession;
    }

    public boolean isJoinEnterprise() {
        return isJoinEnterprise;
    }

    public void setJoinEnterprise(boolean joinEnterprise) {
        isJoinEnterprise = joinEnterprise;
    }

    public boolean isModifyManager() {
        return isModifyManager;
    }

    public void setModifyManager(boolean modifyManager) {
        isModifyManager = modifyManager;
    }

    public String getCartVipType() {
        return cartVipType;
    }

    public void setCartVipType(String cartVipType) {
        this.cartVipType = cartVipType;
    }

    public String getCartSessionType() {
        return cartSessionType;
    }

    public void setCartSessionType(String cartSessionType) {
        this.cartSessionType = cartSessionType;
    }

    public boolean isAdjustVolume() {
        return isAdjustVolume;
    }

    public void setAdjustVolume(boolean adjustVolume) {
        isAdjustVolume = adjustVolume;
    }

    public boolean isCanMultiImage() {
        return isCanMultiImage;
    }

    public void setCanMultiImage(boolean canMultiImage) {
        isCanMultiImage = canMultiImage;
    }

    public String getMultiImage() {
        return multiImage;
    }

    public void setMultiImage(String multiImage) {
        this.multiImage = multiImage;
    }

    public String getDeviceNumberPrefix() {
        return deviceNumberPrefix;
    }

    public void setDeviceNumberPrefix(String deviceNumberPrefix) {
        this.deviceNumberPrefix = deviceNumberPrefix;
    }

    public boolean isAssociate() {
        return isAssociate;
    }

    public void setAssociate(boolean associate) {
        isAssociate = associate;
    }

    public String getSnPrefix() {
        return snPrefix;
    }

    public void setSnPrefix(String snPrefix) {
        this.snPrefix = snPrefix;
    }

    public int getSubType() {
        return subType;
    }

    public void setSubType(int subType) {
        this.subType = subType;
    }

    public boolean isTouchScreen() {
        return isTouchScreen;
    }

    public void setTouchScreen(boolean touchScreen) {
        isTouchScreen = touchScreen;
    }

    public boolean isThirdDevice() {
        return isThirdDevice;
    }

    public void setThirdDevice(boolean thirdDevice) {
        isThirdDevice = thirdDevice;
    }

    public boolean isInspect() {
        return inspect;
    }

    public void setInspect(boolean inspect) {
        this.inspect = inspect;
    }

    /*public boolean isBindConference() {
        return isBindConference;
    }

    public void setBindConference(boolean bindConference) {
        isBindConference = bindConference;
    }*/
}
