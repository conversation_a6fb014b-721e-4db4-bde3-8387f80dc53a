package com.ainemo.libra.dataaccess.po.enterprise;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.ainemo.libra.dataaccess.po.StandardPO;

@Entity
@Table(name="libra_nemo_model")
public class NemoModelPO extends StandardPO {
	private static final long serialVersionUID = 6968171470377554999L;
	@Column(name = "nemo_sn")
	private String nemoSN;
	private String model;
	
	public NemoModelPO() {
		
	}
	
	public NemoModelPO(String nemoSN, String model) {
		this.nemoSN = nemoSN;
		this.model = model;
	}
	
	public String getNemoSN() {
		return nemoSN;
	}
	public void setNemoSN(String nemoSN) {
		this.nemoSN = nemoSN;
	}
	public String getModel() {
		return model;
	}
	public void setModel(String model) {
		this.model = model;
	}
}
