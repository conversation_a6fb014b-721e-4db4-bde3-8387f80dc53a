package com.ainemo.libra.service.impl.nemophone;

import com.ainemo.libra.dataaccess.po.GwDevicePO;
import com.ainemo.libra.dataaccess.po.UserDevicePO;
import com.ainemo.libra.dataaccess.po.UserProfilePO;
import com.ainemo.libra.dataaccess.po.enterprise.ConferenceNumberPO;
import com.ainemo.libra.dataaccess.service.UserDeviceDAO;
import com.ainemo.libra.domain.Mobile;
import com.ainemo.libra.infrastructure.storage.FileStorageService;
import com.ainemo.libra.service.impl.BusinessException;
import com.ainemo.libra.service.impl.ServiceException;
import com.ainemo.libra.service.impl.nemophone.permission.NemoPhonePermissionCheckHandler;
import com.ainemo.libra.service.interfaces.*;
import com.ainemo.libra.service.interfaces.ent.ConferenceManage;
import com.ainemo.libra.service.interfaces.ent.EnterpriseH323InfoProxy;
import com.ainemo.libra.service.interfaces.ent.UserMeetingRoomService;
import com.ainemo.libra.service.interfaces.nemophone.NemoPhoneDistrictStrategy;
import com.ainemo.libra.service.interfaces.nemophone.Result;
import com.ainemo.libra.shared.context.RequestType;
import com.ainemo.libra.shared.context.RestApiContext;
import com.ainemo.libra.shared.enums.DeviceType;
import com.ainemo.libra.shared.enums.ErrorStatus;
import com.ainemo.libra.shared.netstatus.NetStatusHolder;
import com.ainemo.libra.shared.sharemem.NemoPhoneAddressInfoStore;
import com.ainemo.libra.web.api.rest.data.H323Info;
import com.ainemo.libra.web.api.rest.data.UserDevice;
import com.ainemo.libra.web.api.rest.data.buffet.BuffetGwInfo;
import com.ainemo.libra.web.privatecloud.PrivateCloud;
import com.ainemo.protocol.CallUriType;
import com.ainemo.protocol.NumberType;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Created by suikelei on 16/1/9.
 */
public abstract class NemoPhoneCommonStrategy implements NemoPhoneDistrictStrategy {

	private static final String DOUBLE_STAR_SEPARATOR="**";
	private static final String DOUBLE_SHARP_SEPARATOR="##";

//	private static final String H323_SEPARATOR=DOUBLE_STAR_SEPARATOR;
	//To compatible with old version endpoints
	private static final String H323_SEPARATOR=DOUBLE_SHARP_SEPARATOR;

	private Logger logger = Logger.getLogger(NemoPhoneCommonStrategy.class);

	private static Pattern callUrlPattern = Pattern.compile("^(\\w|\\+|-|#|\\*|\\.)+@(SOFT|HARD|BROWSER|TEL|NEMONO|DESK|CONFNO|H323|BRUCE|TVBOX)$");
	private static Pattern nemoNumberPattern = Pattern.compile("^\\d{6}$");
	private static Pattern nemoPhonePattern = Pattern.compile("^(((\\+)?\\d+(\\-\\d+){0,5})||(.*((\\*\\*|##)\\d+){1,5})||(([0-9]{1,3}\\.){3}[0-9]{1,3}))$");
	/**
	 1. IP##网关ID
	 2. IP##会议室号##网关ID
	 3. IP##会议室号##会议密码##网关ID
	 4. E164##网关ID
	 5. IPCID##IP##网关ID         deprecated
	 *
	 */
	private static Pattern h323Pattern = Pattern.compile("^(.*(\\*\\*|##).*)|(([0-9]{1,3}\\.){3}[0-9]{1,3})$");
	private static Pattern bigEndPointPattern = Pattern.compile("^(60|300)\\d{6}$");

	private static final String[] splitH323(String s){
		if(s == null){
			return null;
		}

		int lastDoubleStarPos = s.lastIndexOf(DOUBLE_STAR_SEPARATOR);
		if(lastDoubleStarPos != -1){
			return new String[]{s.substring(0,lastDoubleStarPos),s.substring(lastDoubleStarPos+2)};
		}

		int lastDoubleSharpPos = s.lastIndexOf(DOUBLE_SHARP_SEPARATOR);
		if(lastDoubleSharpPos != -1){
			return new String[]{s.substring(0,lastDoubleSharpPos),s.substring(lastDoubleSharpPos+2)};
		}

		return new String[]{s};
	}

	protected NemoPhoneAddressInfoStore nemoPhoneAddressInfoStore;
	protected NemoConfigService configService;
	protected DeviceService deviceService;
	protected NemoNumberService nemoNumberService;
	protected UserService userService;
	protected GatewayService gatewayService;
	protected UserDeviceDAO userDeviceDAO;
	protected FileStorageService fileStorageService;
	protected String nemoAvatarAccessUrlPrefix;
	protected NemoPhonePermissionCheckHandler nemoPhonePermissionCheckHandler;
	private RestTemplate restTemplate;
	private String h323InfoUrl;
	private Boolean tel2h323;
	private String h323GwId;
	private NemoService nemoService;
	protected ConferenceManage conferenceManage;
	private UserMeetingRoomService userMeetingRoomService;
	private EnterpriseH323InfoProxy enterpriseH323InfoProxy;
	private SiteGatewayService siteGatewayService;
	private Boolean enableSitecodeGW;
	private Boolean enableGWManangerQuery;
	private String siteCodeUri;
	private H323GwGetter h323GwGetter;

	private String defaultAvatarPreix = "http://devcdn.ainemo.com/page/images/deviceavatars/";

	@Override
	public Map<String,Object> handleIncomingCallUrl(String callUri){
		logger.debug("handle Incoming CallUrl:" + callUri);
		return handleIncomingCallUrl(callUri,null,null);
	}

	@Override
	public Map<String,Object> handleIncomingCallUrl(String callUri,String skey){
		logger.debug(" handle incoming callUrl :" + callUri);
		return handleIncomingCallUrl(callUri,skey,null);
	}

	@Override
	public Map<String, Object> handleIncomingCallUrl(String callUri, String skey, String userProfileId) {
		if (!isLegalCallUrl(callUri)) {
			throw new ServiceException("callUri: " + callUri + " is illegal", ErrorStatus.INVALID_PARAMETER);
		}
		CallUriType callUriType = CallUriType.fromCallUri(callUri);
		switch (callUriType) {
			case HARD:
			case TVBOX:
				return parseHardIncomingCallUriInfo(callUri);
			case SOFT:
				return parseSoftIncomingCallUriInfo(callUri);
			case TEL:
				return parseTelIncomingCallUriInfo(callUri);
			case NEMONO:
				return parseNemoIncomingCallUriInfo(callUri);
			case CONFNO:
				return parseConfIncomingCallUriInfo(callUri);
			case DESK:
				return parseDeskIncomingCallUriInfo(callUri);
			case H323:
				return parseH323IncomingCallUriInfo(callUri,skey,userProfileId);
			case BRUCE:
				return parseBruceIncomingCallUriInfo(callUri);
			default:
				return null;
		}
	}

	private Map<String, Object> parseBruceIncomingCallUriInfo(String callUrl) {
		String callNumber = CallUriType.getCallUriPrefix(callUrl);
		Map<String, Object> result = new HashMap<>();

		String disPlayName = "";
		try {
			UserDevice userDevicePO = nemoService.getNemoWithCache(callNumber);
			if(userDevicePO != null && (userDevicePO.getType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue())){
				disPlayName = userDevicePO.getDisplayName();
			}
		} catch (BusinessException e) {
		}
		fillDateForIncomingCall(result, callUrl, NumberType.BRUCE, disPlayName,
				defaultAvatarPreix + "bruce.png",callNumber, CallUriType.BRUCE.name());
		return result;
	}

	private Map<String, Object> parseH323IncomingCallUriInfo(String callUrl,String skey,String userProfileId) {
		String callNumber = CallUriType.getCallUriPrefix(callUrl);
		Map<String, Object> result = new HashMap<>();

		String displayName = getH323DisplayName(callUrl,skey,userProfileId);

		innerFillData(result, callUrl, NumberType.H323, displayName, defaultAvatarPreix + "h323.png");
		result.put(Result.DEVICE_TYPE, CallUriType.H323.name());
		return result;
	}

	private String getH323DisplayName(String callUrl, String skey,String userProfileId){
		try{
			String enterpriseId = null;
			if(StringUtils.isNotBlank(skey)){
				UserDevice userDevice = getCurrentDevice(skey);
				if(userDevice != null){
					enterpriseId = deviceService.getEnterpriseIdByDeviceSN(userDevice);
				}
			}else if(StringUtils.isNotBlank(userProfileId)){
				enterpriseId = userService.getUserProfilePOById(Long.parseLong(userProfileId)).getEnterpriseId();
			}

			if(StringUtils.isNotBlank(enterpriseId)){
				return getH323DisplayName(callUrl,enterpriseId);
			}

		}catch (Exception e){
			logger.error(" get h323 displayName error!,callurl:"+callUrl+",skey:"+skey,e);
		}

		String displayName = StringUtils.EMPTY;
		logger.info(" get h323 displayName return "+ displayName +" ,callurl:"+callUrl+",skey:"+skey);
		return displayName;
	}

	private String getH323DisplayName(String callUrl, String enterpriseId){
		try {
			UserDevicePO userDevicePO = deviceService.getH323Device(splitH323(callUrl)[0], enterpriseId);
			if (userDevicePO != null) {
				return userDevicePO.getDisplayName();
			}
		}catch(Exception e){
			logger.error(" get h323 displayName error!,callurl:"+callUrl+",enterpriseId:"+enterpriseId,e);
		}
		return StringUtils.EMPTY;
	}

	private Map<String, Object> handleConferenceCall(String nemoNumber, String callUrl) {
		try {
			logger.debug("handleConferenceCall :"+ nemoNumber);
			Map<String, Object> result = new HashMap<>();
			Map<String,Object> ans = nemoNumberService.getNemoNumberInfoByConferenceNumber(nemoNumber);
			if(ans != null){
				result.put(Result.ENABLE_PWD, ans.get("enablePwd"));
				String avatar = (String) ans.get(Result.CALL_AVATAR);
				fillDateForIncomingCall(result, callUrl, NumberType.CONFERENCE, ans.get("circleName"), avatar,nemoNumber,null);
			}
			result.put(Result.DEVICE_TYPE, ans.get(NemoNumberService.CALLEE_DEVICE_TYPE));
			return result;
		} catch (BusinessException e) {
			logger.error("parseNemoIncomingCallUriInfo: invalid NEMONO for incomingCallUrl: " + callUrl, e);
			throw new ServiceException("callUri[NEMONO]: " + callUrl + " is illegal", ErrorStatus.INVALID_PARAMETER);
		}
	}
	private Map<String, Object> parseConfIncomingCallUriInfo(String callUrl) {
		String nemoNumber = CallUriType.getCallUriPrefix(callUrl);
		return handleConferenceCall(nemoNumber, callUrl);
	}

	private Map<String, Object> parseNemoIncomingCallUriInfo(String callUrl) {
		String nemoNumber = CallUriType.getCallUriPrefix(callUrl);
		try {
			return attemptParseIncomingNemoCallUriInfo(nemoNumber, callUrl);
		} catch (BusinessException e) {
			logger.error("parseNemoIncomingCallUriInfo: invalid NEMONO for incomingCallUrl: " + callUrl, e);
			throw new ServiceException("callUri[NEMONO]: " + callUrl + " is illegal", ErrorStatus.INVALID_PARAMETER);
		}
	}

	private Map<String, Object> parseTelIncomingCallUriInfo(String callUrl) {
		Map<String, Object> result = new HashMap<>();
		innerFillData(result, callUrl, NumberType.PSTN, StringUtils.EMPTY, StringUtils.EMPTY);
		result.put(Result.DEVICE_TYPE, CallUriType.TEL.name());
		return result;
	}

	private Map<String, Object> parseHardIncomingCallUriInfo(String callUrl) {
		Map<String, Object> result = new HashMap<>();
		String userDeviceId = CallUriType.getCallUriPrefix(callUrl);
		UserDevicePO device = deviceService.getDeviceById((Long.valueOf(userDeviceId)));
		String deviceType = null;
		if (device != null) {
			result.put(Result.DEVICE_ID, userDeviceId);
			String nemoNumber = nemoService.getNemoNumberByDeviceId(device.getId());
			String avatar;
			NumberType numberType;
			if(DeviceType.TVBOX.getValue() == device.getType()){
				avatar = "";
				numberType = NumberType.TVBOX;
				deviceType = CallUriType.TVBOX.name();
			}else {
				UserProfilePO admin = userService.getUserProfilePOById(device.getUserProfileID());
				String nemoAvatar = device.getAvatar();
				String adminAvatar = null;
				if (admin != null) {
					adminAvatar = admin.getProfilePicture();
				}
				avatar = parseNemoAdminAvatar(result, nemoAvatar, adminAvatar);
				numberType = NumberType.NEMO;
				deviceType = CallUriType.HARD.name();
			}
			fillDateForIncomingCall(result, callUrl, numberType, device.getDisplayName(), avatar, nemoNumber, deviceType);
		} else {
			logger.error("getIncomingCallUrlInfo: invalid HARD for incomingCallUrl: " + callUrl);
			throw new ServiceException("callUri[HARD]: " + callUrl + " is illegal", ErrorStatus.INVALID_PARAMETER);
		}

		return result;
	}

	private Map<String, Object> parseSoftIncomingCallUriInfo(String callUrl) {
		return parseSoftDeviceIncomingCallUriInfo(callUrl, NumberType.APP, CallUriType.SOFT);
	}

	private Map<String, Object> parseDeskIncomingCallUriInfo(String callUrl) {
		return parseSoftDeviceIncomingCallUriInfo(callUrl, NumberType.DESK, CallUriType.DESK);
	}

	private Map<String, Object> parseSoftDeviceIncomingCallUriInfo(String callUrl, NumberType numberType, CallUriType callUriType) {
		Map<String, Object> result = new HashMap<>();
		String userProfileId = CallUriType.getCallUriPrefix(callUrl);
		UserProfilePO userProfilePO = userService.getUserProfilePOById(Long.valueOf(userProfileId));
		if (userProfilePO != null) {
			fillDateForIncomingCall(result, callUrl, numberType, userProfilePO.getDisplayName(),
					parseAppAvatar(userProfilePO.getProfilePicture()), StringUtils.isBlank(userProfilePO.getCellPhone()) ? userProfilePO.getCallNum() : StringUtils.trimToEmpty(userProfilePO.getCellPhone()), callUriType.name());
		} else {
			throw new ServiceException("callUri[SOFT]: " + callUrl + " is illegal", ErrorStatus.INVALID_PARAMETER);
		}
		result.put(Result.COUNTRY_CODE,userProfilePO.getCountryCode());
		return result;
	}
	private boolean isLegalCallUrl(String number) {
		if (StringUtils.isBlank(number)) {
			return false;
		}
		return callUrlPattern.matcher(number).matches();
	}

	protected boolean isNemoNumber(String number) {
		return nemoNumberPattern.matcher(number).matches();
	}

	protected boolean isNemoPhone(String number) {
		return nemoPhonePattern.matcher(number).matches();
	}

	@Override
	public Map<String, Object> handleNemoPhoneNumber(String number, String passwd, String areacode, String skey, String version, String deviceType,String enterpriseId) {
		if(conferenceManage != null) {
			ConferenceNumberPO conferenceNumberPO = conferenceManage.getConferenceNumberByShareCode(number);
			if (conferenceNumberPO != null) {
				return handleConferenceCall(conferenceNumberPO.getNumber(),
						parseCallUri(conferenceNumberPO.getNumber(), CallUriType.CONFNO));
			}
		}

		validateNemoPhone(number);
		Map<String, Object> result;
		if (number.startsWith("+")) {
			result = handleNemoPhoneWithCountryCode(number, skey, deviceType);
		} else {
			result = handleCommonNemoPhoneNumber(number, passwd, areacode, skey, version, deviceType,enterpriseId);
		}
		CallUriType callUriType = CallUriType.fromCallUri(((String)result.get(Result.CALL_URL)));

		if(PrivateCloud.isPrivate() && tel2h323 && callUriType == CallUriType.TEL){
			// special config
			//私有云下只能访问企业私有网关，所以企业id不能为空
			if(StringUtils.isBlank(enterpriseId)){
				UserDevice deviceInfo = getCurrentDevice(skey);
				if(deviceInfo != null){
					enterpriseId = deviceService.getEnterpriseIdByDeviceSN(deviceInfo);
				}
			}
			if(StringUtils.isBlank(enterpriseId)){
				logger.error(" enterprise is null in private cloud !");
				throw new ServiceException("enterprise is null in private cloud !",ErrorStatus.H323_GATE_WAY_FAILURE);
			}
			String h323GWNumber;
			BuffetGwInfo[] enterpriseGWInfo = enterpriseH323InfoProxy.getEnterpriseGWInfo(enterpriseId);
			if(enterpriseGWInfo != null && enterpriseGWInfo.length > 0) {
				h323GWNumber = enterpriseGWInfo[0].getNumber();
				result.put(Result.CALL_URL, number+H323_SEPARATOR+h323GWNumber+"@"+NumberType.H323.name());
				result.put(Result.CALL_NUMBER, number+H323_SEPARATOR+h323GWNumber);
				GwDevicePO gwDevicePO = gatewayService.getGwDeviceByNumberAndType(h323GWNumber, GwDevicePO.GwType.H323);
				if(gwDevicePO != null && StringUtils.isNotBlank(gwDevicePO.getPwd()) && !StringUtils.equals(gwDevicePO.getPwd(),passwd)){
					result.put(Result.ENABLE_PWD, true);
				}
			}

			result.put(Result.NUMBER_TYPE, NumberType.H323);
			result.put(Result.CALL_AVATAR, defaultAvatarPreix + "h323.png");
			result.put(Result.DEVICE_TYPE, CallUriType.H323.name());

		}

		//私有云
		if(enableSitecodeGW){
			String siteGw = null;
			try {
				String ip = NetStatusHolder.getNetStatus().getRemoteIP();
				String nginxIP = NetStatusHolder.getNetStatus().getNginxIP();
				Map<String,String> sitecodeMap =  restTemplate.getForObject(siteCodeUri+"/api/rest/internal/v1/sitecode/location?nginxip="+nginxIP+"&endpointip="+ip,Map.class);//= rest
				String sitecode;
				if("OK".equals(sitecodeMap.get("status"))){
					sitecode = sitecodeMap.get("location");
					siteGw = siteGatewayService.getGatewayBySite(sitecode);
				}

				if(StringUtils.isEmpty(siteGw)){
					siteGw = h323GwId;
				}
			}catch (Exception e){
				logger.error("get gateway by sitecode error",e);
				siteGw = h323GwId;
			}

			result.put(Result.CALL_URL, number+H323_SEPARATOR+siteGw+"@"+NumberType.H323.name());
			result.put(Result.CALL_NUMBER, number+H323_SEPARATOR+siteGw);

			GwDevicePO gwDevicePO = gatewayService.getGwDeviceByNumberAndType(siteGw, GwDevicePO.GwType.H323);

			if(gwDevicePO != null && StringUtils.isNotBlank(gwDevicePO.getPwd()) && !StringUtils.equals(gwDevicePO.getPwd(),passwd)){
				result.put(Result.ENABLE_PWD, true);
			}else{
				result.put(Result.ENABLE_PWD, false);
			}
		}

		if(skey != null && !skey.trim().isEmpty()) {
			nemoPhonePermissionCheckHandler.checkNemoPhoneCallPermission(result,number,skey.trim(), deviceType);
		}
		return result;
	}

	private Map<String, Object> handleCommonNemoPhoneNumber(String number, String passwd, String areacode, String skey, String version, String deviceType,String enterpriseId){
		Map<String, Object> result = handleH323Number(skey,number, passwd,enterpriseId);
		if(result == null){
			result = handleBigEndPointNumber(number);
			if(result == null){
				result = handleLocalAreaNemoPhone(number, passwd, areacode, skey, version, deviceType);
			}
		}
		return result;
	}

	private boolean isH323Number(String number) {
		return h323Pattern.matcher(number).matches();
	}

	private boolean isBigEndPointNumber(String number){
		return bigEndPointPattern.matcher(number).matches();
	}

	@Override
	public Map<String, Object> handleH323Number(String skey, String number, String passwd,String enterpriseId){
		if(!isH323Number(number))
			return null;

		String h323GWNumber = null;

		if(number.contains(DOUBLE_STAR_SEPARATOR) || number.contains(DOUBLE_SHARP_SEPARATOR)){
			String[] parts = splitH323(number);
			h323GWNumber = parts[parts.length-1];
		}else {
			if(StringUtils.isBlank(enterpriseId)){
				UserDevice deviceInfo = getCurrentDevice(skey);
				if(deviceInfo != null){
					enterpriseId = deviceService.getEnterpriseIdByDeviceSN(deviceInfo);
				}
			}

			if(StringUtils.isNotBlank(enterpriseId)){
				// May be gateway number or gateway manager number
				BuffetGwInfo[] enterpriseGWInfo = enterpriseH323InfoProxy.getEnterpriseGWInfo(enterpriseId);
				if(enterpriseGWInfo != null && enterpriseGWInfo.length > 0){
					h323GWNumber = h323GwGetter.getGwNumber(enterpriseGWInfo);
				}else {
					if(PrivateCloud.isPrivate()){
						h323GWNumber = h323GwGetter.getDefaultPrdGwNumber();;
					}else {
						long h323Count = enterpriseH323InfoProxy.getH323CountByEnterpriseId(enterpriseId);
						if (h323Count > 0) {
							h323GWNumber = h323GwGetter.getDefaultPrdGwNumber();
						}
					}
				}
			}else{
				if(PrivateCloud.isPrivate()){
					logger.error(" enterprise can not be null in private cloud when getting gate way!");
					throw new ServiceException("enterprise can not be null in private cloud when getting gate way!",ErrorStatus.H323_GATE_WAY_FAILURE);
				}
			}

			if(StringUtils.isBlank(h323GWNumber)){
				h323GWNumber = h323GwGetter.getTestGwNumber();
			}
			number =  number+ H323_SEPARATOR+ h323GWNumber;
		}

		Map<String, Object> result = new HashMap<>();
		if(StringUtils.isNotBlank(enterpriseId)){
			result.put(Result.DISPLAY_NAME,getH323DisplayName(number,enterpriseId));
		}else{
			result.put(Result.DISPLAY_NAME,getH323DisplayName(number,skey,null));
		}

		GwDevicePO gwDevicePO = gatewayService.getGwDeviceByNumberAndType(h323GWNumber, GwDevicePO.GwType.H323);
		if(gwDevicePO != null && StringUtils.isNotBlank(gwDevicePO.getPwd()) && !StringUtils.equals(gwDevicePO.getPwd(),passwd)){
			result.put(Result.ENABLE_PWD, true);
		}
		result.put(Result.CALL_URL, number+"@"+NumberType.H323.name());
		result.put(Result.NUMBER_TYPE, NumberType.H323);
		result.put(Result.CALL_NUMBER, number);
		result.put(Result.CALL_AVATAR, defaultAvatarPreix + "h323.png");
		result.put(Result.DEVICE_TYPE, CallUriType.H323.name());

		return result;
	}

	private Map<String, Object> handleBigEndPointNumber(String number){
		Map<String, Object> result = null;
		if(isBigEndPointNumber(number)){
			try {
				UserDevice userDevicePO = nemoService.getNemoWithCache(number);
				if(userDevicePO != null && (userDevicePO.getType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue()) ){
					result = new HashMap<>();
					result.put(Result.CALL_URL, number+"@"+NumberType.BRUCE.name());
					result.put(Result.DEVICE_ID,userDevicePO.getId());
					result.put(Result.NUMBER_TYPE, NumberType.BRUCE);
					result.put(Result.CALL_NUMBER, number);
					result.put(Result.DISPLAY_NAME, userDevicePO.getDisplayName());
					result.put(Result.CALL_AVATAR, defaultAvatarPreix + "bruce.png");
					result.put(Result.DEVICE_TYPE, CallUriType.BRUCE.name());
				}
			} catch (Exception e) {
			}
		}
		return result;
	}

	private H323Info getH323Info(String identifier) {
		try {
			URI uri= UriComponentsBuilder.fromHttpUrl(h323InfoUrl)
					.queryParam("identifier", identifier).build().toUri();
			ResponseEntity<H323Info> responseEntity = restTemplate.getForEntity(uri, H323Info.class);
			if(responseEntity.getStatusCode() == HttpStatus.OK) {
				return responseEntity.getBody();
			}
		} catch (Throwable t) {
			logger.error("Failed to get h323 info by identifier: " + identifier, t);
		}
		return null;
	}

	private void validateNemoPhone(String number) {
		if(!isNemoPhone(number)){
			throw new ServiceException("callNumber: "+ number + " is illegal", ErrorStatus.INVALID_NUMBER);
		}
	}



	private Map<String, Object> handleNemoPhoneWithCountryCode(String number, String skey, String deviceType) {
		logger.info("handleNemoPhoneWithCountryCode with number: " + number + ", skey: " + skey);
		int currentContext = getCurrentContext(skey, deviceType);
		return handleNemoPhoneWithCountryCode(number, currentContext);
	}

	@Override
	public Map<String, Object> handleNemoPhoneWithCountryCode(String number, int currentContext) {
		Mobile mobile = Mobile.generatePossibleMobile(number);
		String countryCode = mobile.countryCode;
		String phone = mobile.phone;
		if(StringUtils.isEmpty(countryCode)){
			String[] splits = phone.split("-");
			if(splits.length == 2){
				countryCode = splits[0];
				phone = splits[1];
			}
		}
		return parsePhoneCallUrlInfo(number, countryCode, phone, currentContext);
	}

	private String taiwanInternationalCallSpecialConfig(String countryCode, String phoneNumber) {
		if("+886".equals(countryCode) && phoneNumber.startsWith("9")){
			phoneNumber = "0"+phoneNumber;
		}
		return phoneNumber;
	}

	protected Map<String,Object> parseUnknownNumberCallUrlInfo(String countryCode, String number, String areaCode, String skey, String deviceType) {
		logger.info("unknownNumber for input number: "+number+",areaCode: "+areaCode+",try to appcall");
		try{
			return attemptParsePhoneCallUrlInfo(countryCode,number,getCurrentContext(skey,deviceType));
		}catch(BusinessException e){
			logger.info("parseUnknownNumber: not find app, continue parse unknownNumber for input number: "+number+",areaCode: "+areaCode);
		}
		return parseUnknownPSTNNumberCallUrlInfo(number,countryCode,areaCode,number);
	}

	protected Map<String,Object> parsePSTNNumberCallUrlInfo(String callNumber, String countryCode, String number) {
		logger.info("parse pstn number for input number: "+number + ", countryCode: "+countryCode);
		Map<String,Object> result = new HashMap<>();
		fillData(result, parseCallUri(parseFullNumber(countryCode,number), CallUriType.TEL), NumberType.PSTN,
				StringUtils.EMPTY, StringUtils.EMPTY, callNumber, CallUriType.TEL.name());
		return result;
	}

	protected Map<String,Object> parseUnknownPSTNNumberCallUrlInfo(String callNumber, String countryCode, String areaCode, String number) {
		logger.info("parse unknown number for input number: "+number + ", countryCode: "+countryCode);
		Map<String,Object> result = new HashMap<>();
		fillData(result, parseCallUri(parseFullNumber(countryCode,(areaCode==null || "null".equals(areaCode))?number:areaCode+number), CallUriType.TEL), NumberType.PSTN,
				StringUtils.EMPTY, StringUtils.EMPTY,callNumber, CallUriType.TEL.name());
		return result;
	}

	protected Map<String, Object> parsePhoneCallUrlInfo(String callNumber, String countryCode, String phoneNumber, int currentContext) {
		logger.info("parsePhoneCallUrlInfo for input phoneNumber: " + phoneNumber + ", countryCode: " + countryCode + ", currentContext: "+currentContext);
		Map<String, Object> result = new HashMap<>();
		UserProfilePO userProfilePO = getUserByPhone(countryCode, phoneNumber, currentContext);
		if (userProfilePO != null) {
			fillData(result, parseCallUri(userProfilePO.getId() + "", CallUriType.SOFT), NumberType.APP,
					userProfilePO.getDisplayName(), parseAppAvatar(userProfilePO.getProfilePicture()), callNumber, CallUriType.SOFT.name());
			logger.info("parsePhoneCallUrlInfo find app, turn to app call");
		} else {
			phoneNumber = taiwanInternationalCallSpecialConfig(countryCode, phoneNumber);
			fillData(result, parseCallUri(parseFullNumber(countryCode, phoneNumber), CallUriType.TEL), NumberType.PSTN,
					StringUtils.EMPTY, StringUtils.EMPTY, callNumber, CallUriType.TEL.name());
			logger.info("parsePhoneCallUrlInfo not find app, continue pstn call");
		}
		return result;
	}

	protected Map<String, Object> attemptParsePhoneCallUrlInfo(String countryCode, String phoneNumber, int currentContext) throws BusinessException {
		logger.info("attemptParsePhoneCallUrlInfo for input phoneNumber: " + phoneNumber + ", countryCode: " + countryCode + ", currentContext: "+currentContext);
		Map<String, Object> result = new HashMap<>();
		UserProfilePO userProfilePO = getUserByPhone(countryCode, phoneNumber, currentContext);
		if (userProfilePO != null) {
			fillData(result, parseCallUri(userProfilePO.getId() + "", CallUriType.SOFT), NumberType.APP, userProfilePO.getDisplayName(),
					parseAppAvatar(userProfilePO.getProfilePicture()),phoneNumber, CallUriType.SOFT.name());
			logger.info("attemptParsePhoneCallUrlInfo: find app, turn to app call");
		} else {
			throw new BusinessException("attemptParsePhoneCallUrlInfo: app not registered",ErrorStatus.INVALID_PHONE_NOT_REGISTERED);
		}
		return result;
	}

	protected Map<String, Object> attemptParseIncomingNemoCallUriInfo(String number, String CallUrl) throws BusinessException{
		Map<String, Object> result = attemptParseNemoCallUriInfo(number,"","");
		result.put(Result.CALL_URL,CallUrl);
		return result;
	}

	@Override
	public Map<String, Object> handleNemoCallUriInfo(String number) throws BusinessException{
		return attemptParseNemoCallUriInfo(number, "", "v4");
	}

	protected Map<String, Object> attemptParseNemoCallUriInfo(String number, String passwd, String version) throws BusinessException {
		logger.info("attemptParseNemoCallUriInfo for input number: " + number);
		String callNumber = number;
		Map<String, Object> result = new HashMap<>();
		Map<String,Object> ans = nemoNumberService.getNemoNumberInfo(number);
		NumberType numberType = null;
		CallUriType callUriType = CallUriType.NEMONO;
		String calleeDeviceType = (String)ans.get(NemoNumberService.CALLEE_DEVICE_TYPE);

		String avatar = (String) ans.get(Result.CALL_AVATAR);
		if (ans.get("conferenceNumber") != null) {
			result.put(Result.ENABLE_PWD, (Boolean) ans.get("enablePwd"));
			addPwdResult(passwd, result, ans);
			numberType = NumberType.CONFERENCE;
			if("v4".equals(version)){
				callUriType = CallUriType.CONFNO;
			}
		} else if (ans.get("virtualNemoNumber") != null) {
			numberType = NumberType.VIRTUALNEMO;
			callUriType = CallUriType.NEMONO;
		} else {
			result.put(Result.DEVICE_ID, ans.get("deviceId"));
			result.put(Result.NUMBER, ans.get("nemoNumber"));
			result.put(Result.ENABLE_PWD, (Boolean) ans.get("enablePwd"));
			addPwdResult(passwd, result, ans);
			numberType = NumberType.NEMO;
			Object deviceType = ans.get("deviceType");
			avatar = parseNemoAdminAvatar(result, (String) ans.get("nemoAvatar"), (String) ans.get("adminPicture"));

			if("v4".equals(version)){
				if(isTvBox(deviceType)){
					number = ans.get("deviceId")+"";
					callUriType = CallUriType.TVBOX;
					numberType = NumberType.TVBOX;
				} else if(isBruce(deviceType)){
					callUriType = CallUriType.BRUCE;
					numberType = NumberType.BRUCE;
				}else {
					number = ans.get("deviceId")+"";
					callUriType = CallUriType.HARD;
				}
			} else {
				if(isTvBox(deviceType)){
					callUriType = CallUriType.TVBOX;
					numberType = NumberType.TVBOX;
				} else if(isBruce(deviceType)){
					callUriType = CallUriType.BRUCE;
					numberType = NumberType.BRUCE;
				}
			}
		}
		if(calleeDeviceType == null) {
			calleeDeviceType = callUriType.name();
		}
		fillData(result, parseCallUri(number, callUriType), numberType, ans.get("circleName"), avatar, callNumber, calleeDeviceType);
		return result;
	}

//	0 不需要密码,1 需要密码,未输入 2需要密码,输入错误 3 需要密码,输入正确
	private void addPwdResult(String passwd, Map<String, Object> result, Map<String, Object> ans) {
		if(!(Boolean)result.get(Result.ENABLE_PWD)){
			result.put(Result.PWD_RESULT,0);
		}else {
			if(StringUtils.isEmpty(passwd)){
				result.put(Result.PWD_RESULT,1);
			}else if(!StringUtils.equals((String)ans.get("passwd"),passwd)){
				result.put(Result.PWD_RESULT,2);
			}else {
				result.put(Result.PWD_RESULT,3);
			}
		}
	}

	private boolean isTvBox(Object deviceType) {
		return deviceType != null && Integer.parseInt((String) deviceType) == DeviceType.TVBOX.getValue();
	}

	private boolean isBruce(Object deviceType) {
		return deviceType != null && Integer.parseInt((String) deviceType) == DeviceType.BIG_ENDPOINT_DEVICE.getValue();
	}

	protected String parseCallUri(String number, CallUriType type) {
		return number + "@" + type.name();
	}

	private void innerFillData(Map<String, Object> result, Object callUrl, NumberType numberType, Object displayName, String avatar) {
		result.put(Result.CALL_URL, callUrl);
		result.put(Result.NUMBER_TYPE, numberType);
		result.put(Result.DISPLAY_NAME, displayName);
		result.put(Result.CALL_AVATAR, avatar);
	}

	protected void fillData(Map<String, Object> result, Object callUrl, NumberType numberType, Object displayName,
							String avatar, String callNumber, String deviceType) {
		innerFillData(result,callUrl,numberType,displayName,avatar);
		result.put(Result.CALL_NUMBER, callNumber);
		result.put(Result.DEVICE_TYPE, deviceType);
	}

	protected void fillDateForIncomingCall(Map<String, Object> result, Object callUrl, NumberType numberType,
										   Object displayName, String avatar, String number, String deviceType) {
		innerFillData(result, callUrl, numberType, displayName, avatar);
		result.put(Result.NUMBER, number);
		if(StringUtils.isNotEmpty(deviceType)){
			result.put(Result.DEVICE_TYPE, deviceType);
		}
	}

	protected String parseAppAvatar(String avatar) {
		if (StringUtils.isBlank(avatar)) {
			return StringUtils.EMPTY;
		}
		return fileStorageService.getRelativeURL(avatar);
	}

	private String parseNemoAdminAvatar(Map<String, Object> result, String nemoAvatar, String adminAvatar) {
		String avatar = deviceService.getFullPathNemoAvatar(nemoAvatar);
		result.put(Result.AVATAR_OWNER, Result.AvatarOwner.NEMO.name());
		if (StringUtils.isBlank(avatar)) {
			result.put(Result.AVATAR_OWNER, Result.AvatarOwner.ADMIN.name());
			avatar = parseAppAvatar(adminAvatar);
		}
		if (StringUtils.isBlank(avatar)) {
			result.put(Result.AVATAR_OWNER, StringUtils.EMPTY);
		}
		return avatar;
	}

	protected String parseFullNumber(String countryCode, String phone) {
		if(StringUtils.isEmpty(countryCode)){
			return phone;
		}
		return countryCode + "-" + phone;
	}

	protected UserDevice getCurrentDevice(String skey) {
		return deviceService.getUserDeviceBySecurityKey(skey);
	}

	protected int getCurrentContext(String skey, String deviceType) {
		if(StringUtils.isEmpty(skey)){
			return RequestType.ENTERPRISE.getType();
		}
		int currentContext = RequestType.ENTERPRISE.getType();
		if(StringUtils.isNotEmpty(deviceType)){
			if(Integer.valueOf(deviceType) == DeviceType.BIG_ENDPOINT_DEVICE.getValue()){
				return RequestType.ENTERPRISE.getType();
			}
			if(Integer.valueOf(deviceType) == DeviceType.GW_H323.getValue()){
				return RequestType.ENTERPRISE.getType();
			}
		}

		UserDevice deviceInfo = getCurrentDevice(skey);
		if (deviceInfo.getType() == DeviceType.SOFT.getValue()) {
			currentContext = RestApiContext.getRequestType().getType();
		} else if (DeviceType.isHardDevice(deviceInfo.getType())) {
			currentContext = deviceService.isEnterpriseNemo(deviceInfo.getDeviceSN()) ? RequestType.ENTERPRISE.getType() : RequestType.HOME.getType();
		}

		logger.info("currentContext: " + currentContext);
		return currentContext;
	}

	protected UserProfilePO getUserByPhone(String countryCode, String number, int currentContext) {
		Mobile mobile = Mobile.generateMobile(countryCode, number);
		UserProfilePO user = userService.getUserProfilePOByMobile(mobile, currentContext);
		if(user == null) {
			user = userService.getUserProfilePOByMobile(mobile,
					RequestType.otherType(RequestType.valueOf(currentContext)).getType());
		}
		if(user == null) {
			user = userService.getUserProfilePOByCallNum(number);
		}
		if(user != null) {
			logger.info("getUserByPhone: find user by: " + number + ",current context: " + user.getType());
		}
		return user;
	}

	@Override
	public Map<String, Object> getUserMeetingRoomCallUrlInfo(long userId) throws BusinessException{
		List<ConferenceNumberPO> conferenceNumberPOS = userMeetingRoomService.getUserMeetingRooms(userId);
		if(conferenceNumberPOS == null || conferenceNumberPOS.size() == 0) {
			throw new ServiceException("No room found by user: " + userId, ErrorStatus.INVALID_PARAMETER);
		}
		ConferenceNumberPO conferenceNumberPO = conferenceNumberPOS.get(0);
		return attemptParseNemoCallUriInfo(conferenceNumberPO.getNumber(), conferenceNumberPO.getPassword(), "v4");
	}

	protected abstract Map<String, Object> handleLocalAreaNemoPhone(String number, String passwd, String areaCode, String skey, String version, String deviceType);

	public void setDeviceService(DeviceService deviceService) {
		this.deviceService = deviceService;
	}

	public void setUserService(UserService userService) {
		this.userService = userService;
	}

	public void setFileStorageService(FileStorageService fileStorageService) {
		this.fileStorageService = fileStorageService;
	}

	public void setNemoNumberService(NemoNumberService nemoNumberService) {
		this.nemoNumberService = nemoNumberService;
	}

	public void setNemoAvatarAccessUrlPrefix(String nemoAvatarAccessUrlPrefix) {
		this.nemoAvatarAccessUrlPrefix = nemoAvatarAccessUrlPrefix;
	}

	public void setUserDeviceDAO(UserDeviceDAO userDeviceDAO) {
		this.userDeviceDAO = userDeviceDAO;
	}

	public void setNemoPhoneAddressInfoStore(NemoPhoneAddressInfoStore nemoPhoneAddressInfoStore) {
		this.nemoPhoneAddressInfoStore = nemoPhoneAddressInfoStore;
	}

	public void setConfigService(NemoConfigService configService) {
		this.configService = configService;
	}

	public void setNemoPhonePermissionCheckHandler(NemoPhonePermissionCheckHandler nemoPhonePermissionCheckHandler) {
		this.nemoPhonePermissionCheckHandler = nemoPhonePermissionCheckHandler;
	}

	public void setRestTemplate(RestTemplate restTemplate) {
		this.restTemplate = restTemplate;
	}

	public void setH323InfoUrl(String h323InfoUrl) {
		this.h323InfoUrl = h323InfoUrl;
	}

	public void setTel2h323(Boolean tel2h323) {
		this.tel2h323 = tel2h323;
	}

	public void setH323GwId(String h323GwId) {
		this.h323GwId = h323GwId;
	}

	public void setNemoService(NemoService nemoService) {
		this.nemoService = nemoService;
	}

	public void setDefaultAvatarPreix(String defaultAvatarPreix) {
		this.defaultAvatarPreix = defaultAvatarPreix;
	}

	public void setConferenceManage(ConferenceManage conferenceManage) {
		this.conferenceManage = conferenceManage;
	}

	public void setEnterpriseH323InfoProxy(EnterpriseH323InfoProxy enterpriseH323InfoProxy) {
		this.enterpriseH323InfoProxy = enterpriseH323InfoProxy;
	}

	public void setUserMeetingRoomService(UserMeetingRoomService userMeetingRoomService) {
		this.userMeetingRoomService = userMeetingRoomService;
	}

	public void setSiteGatewayService(SiteGatewayService siteGatewayService) {
		this.siteGatewayService = siteGatewayService;
	}

	public void setGatewayService(GatewayService gatewayService) {
		this.gatewayService = gatewayService;
	}

	public void setEnableSitecodeGW(Boolean enableSitecodeGW) {
		this.enableSitecodeGW = enableSitecodeGW;
	}

	public void setEnableGWManangerQuery(Boolean enableGWManangerQuery) {
		this.enableGWManangerQuery = enableGWManangerQuery;
	}

	public void setSiteCodeUri(String siteCodeUri) {
		this.siteCodeUri = siteCodeUri;
	}

	public void setH323GwGetter(H323GwGetter h323GwGetter) {
		this.h323GwGetter = h323GwGetter;
	}
}
