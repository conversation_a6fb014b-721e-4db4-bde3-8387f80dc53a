package com.ainemo.libra.service.interfaces;

import java.util.Set;

import com.ainemo.protocol.Device;
import com.ainemo.protocol.statistics.TotalStatisReport;


public interface CallCollectorService {
	
	public boolean addCallWhenStartCallOrAudio(String callId,long callTime,boolean isValidStart,int expireSecond);

	public boolean updateCallWhenAudioToVideo(String callId,long timestamp);
	
	public boolean delCall(String callId);
	
	public String getCall(String callId);
	
	public void saveTotalStatisReport(TotalStatisReport totalStatisReport);
	
	public TotalStatisReport getTotalStatisReport();
	
	public void incallNemoChange(String nemoId, boolean isIncall);

	Set<String> getInCallNemoSet();

	boolean isNemoInCall(String nemoId);
	
	boolean isFixedMainNemo(String nemoNumber);
	
	void addFixedMainNemo(String nemoNumber);
	
	void removeFixedMainNemo(String nemoNumber);
	
	Set<String> getFixedMainNemos();
	
	public void incallNemoUserChange(String nemoId, Device device, long timeStamp, boolean join);
	
	public Set<String> getInCallDevices(String nemoId);
	
//	public InCallDevice getIncallDevice(String nemoId, String deviceKey);
}
