package com.ainemo.libra.service.interfaces;

import com.ainemo.libra.dataaccess.DataAccessException;
import com.ainemo.libra.dataaccess.po.UserDevicePO;
import com.ainemo.libra.dataaccess.po.UserProfilePO;
import com.ainemo.libra.service.impl.ServiceException;
import com.ainemo.libra.service.util.SnAndFingerprint;

import java.util.Map;

/**
 * Created by root on 3/15/16.
 */
public interface HardwareBindService {
    enum Action {
        Bind("bind"),
        Unbind("unbind");

        private String value;

        Action(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * Bind a physical nemo. Creates a virtual nemo connected to that physical nemo at the same time.
     *
     * @param userId
     * @param snAndFpt
     * @return
     * @throws ServiceException
     */
    UserDevicePO bindHardDevice(long userId, long deviceId, SnAndFingerprint snAndFpt) throws ServiceException;

    /**
     * The deviceType here is HARD or TVBOX in {@link com.ainemo.libra.shared.enums.DeviceType}
     * @param userId
     * @param deviceId
     * @param snAndFpt
     * @param deviceType
     * @return
     */
    UserDevicePO bindHardDevice(long userId, long deviceId, SnAndFingerprint snAndFpt, int deviceType);

    UserDevicePO bindHardDevice(long userId, long deviceId, SnAndFingerprint snAndFpt, String nemoNumberToUse);

    UserDevicePO bindHardDevice(long userId, long deviceId, SnAndFingerprint snAndFpt, int deviceType, String deviceDisplaName);

    UserDevicePO bindCMCCDevice(UserProfilePO userProfilePO, SnAndFingerprint snAndFgp);

    /**
     * Detach a physical nemo from a virtual nemo
     *
     * @param userId
     * @param deviceSn
     * @return
     * @throws ServiceException
     */
    UserDevicePO unbindHardDevice(long userId, String deviceSn) throws ServiceException;

    void deleteHardDevice(long userId,String deviceSn) throws ServiceException;

    /**
     * Remove a virtual nemo.
     *
     * @param userId
     * @param virtualNemoId
     */
    void removeVirtualNemo(long userId,long virtualNemoId) throws ServiceException;

    Map<Object,Object> logStatis(long deviceId, long userId, Action action);

    UserDevicePO bindNemo(long userId, SnAndFingerprint snAndFgp,
                          UserDevicePO devicePO,
                          String deviceDisplayName,
                          boolean newBind,
                          long restoreDeviceId,
                          boolean checkUseMode) throws DataAccessException;

    UserDevicePO unbindNemo(UserDevicePO device, long userId) throws DataAccessException;

    void deleteDevice(long userProfileId, long nemoId);

    void resetDevice(UserDevicePO userDevicePO);

    UserDevicePO internalUnbindDevice(Long userId,String nemoSn)throws DataAccessException;
}
