package com.ainemo.libra.service.interfaces;

import com.ainemo.libra.web.api.rest.data.Device;

import java.util.List;

/**
 * <AUTHOR>
 * @description 设备黑名单
 * @create 2019/2/21
 */
public interface DeviceBlacklistService {

    /**
     * 将设备添加到设备黑名单中
     *
     * @param deviceSn
     */
    void addToDeviceBlacklist(String deviceSn);

    /**
     * 将设备移除设备黑名单
     *
     * @param deviceSn
     */
    void removeToDeviceBlacklist(String deviceSn);

    /**
     * 将设备添加到匿名设备黑名单
     * @param deviceSn
     */
    void addAnonymousBlackList(String deviceSn);

    /**
     * 是否属于匿名设备黑名单
     * @param deviceSn
     * @return
     */
    boolean isInAnonymousBlackList(String deviceSn);

    /**
     * 将设备移除匿名设备黑名单
     * @param deviceSn
     */
    void removeToAnonymousBlackList(String deviceSn);

    /**
     * 查询所有匿名黑名单sn
     * @return
     */
    List<Device> getAllAnonymousBlackList();
}
