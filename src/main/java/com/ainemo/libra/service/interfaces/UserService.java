package com.ainemo.libra.service.interfaces;

import com.ainemo.iauth.web.api.rest.resource.info.ExpandEncryptChangePasswordRequest;
import com.ainemo.libra.dataaccess.DataAccessException;
import com.ainemo.libra.dataaccess.po.BlackAccountPO;
import com.ainemo.libra.dataaccess.po.UserDevicePO;
import com.ainemo.libra.dataaccess.po.UserProfileField;
import com.ainemo.libra.dataaccess.po.UserProfilePO;
import com.ainemo.libra.dataaccess.po.enterprise.contact.UserProfileExtPO;
import com.ainemo.libra.dataaccess.po.user.UserMailValidatePO;
import com.ainemo.libra.dataaccess.po.user.UserPO;
import com.ainemo.libra.dataaccess.util.DeviceOperationType;
import com.ainemo.libra.domain.Channel;
import com.ainemo.libra.domain.Mobile;
import com.ainemo.libra.domain.RequestVerificationCode;
import com.ainemo.libra.service.impl.ServiceException;
import com.ainemo.libra.service.model.AuthorityRule;
import com.ainemo.libra.service.model.SmsFunctionType;
import com.ainemo.libra.service.model.trace.TrackPoint;
import com.ainemo.libra.service.util.SnAndFingerprint;
import com.ainemo.libra.shared.context.RequestType;
import com.ainemo.libra.shared.enums.Privilege;
import com.ainemo.libra.web.api.rest.data.msg.FriendRequest;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface UserService extends UserBaseService {


    UserProfilePO getUserProfilePOByUserPhone(String cellPhone);

    enum FriendState {
        REQUESTING, REQUESTED, FRIEND,
        ;
    }

    void changePwd(String identifier, String pwd, String newPwd, String customizedKey) throws ServiceException;

    void changePwdWithCheckPasswordEnhance(String identifier, String pwd, String newPwd, String customizedKey) throws ServiceException;
    /**
     * 密码修改
     *
     * @param identifier
     * @param pwd
     * @param newPwd
     * @param customizedKey
     * @param encryptedPassword
     * @param enhancePassword
     * @throws ServiceException
     */
    void changePwd(String identifier, String pwd, String newPwd, String customizedKey, boolean encryptedPassword, boolean enhancePassword) throws ServiceException;

    /**
     * 根据手机号更新用户密码，sdk用
     *
     * @param userPhone 用户手机号
     * @param newPwd    新密码
     * @throws ServiceException .
     * <AUTHOR>
     * @date 2018-10-17
     */
    @Deprecated
    void changePwd(String userPhone, String newPwd) throws ServiceException;

    void resetPwd(ExpandEncryptChangePasswordRequest request, String customizedKey) throws ServiceException;

    void resetPwd(String identifier,
                  String verificationCode,
                  String newPwd,
                  String customizedKey,
                  int deviceType,
                  String deviceSn) throws ServiceException;

    void changePwdWithoutOldPwd(String identifier, String newPwd, boolean encryptedPassword, boolean enhancePassword, boolean checkEnhancePassword) throws ServiceException;

    void changeUserProfiles(long userProfileId, List<UserProfileField> fields) throws ServiceException;

    UserProfilePO getUserProfileByUserId(String userId);


    UserProfilePO getUserProfileByUserId(String userId, int type);

    UserProfileExtPO getUserProfileExtPOByUserProfileId(long id);

    List<UserProfilePO> getListByIds(List<Long> ids);

    List<UserProfilePO> getValidUserProfileByEnterpriseId(String enterpriseId);

    UserProfilePO getUserProfilePOBySecurityKey(String securityKey);

    List<UserProfilePO> getUserProfilePOByMobile(Mobile mobile);

    void addUserRelationshipRequest(long ownerId, long buddyId, String msg, List<Long> nemos, Map<Long, List<AuthorityRule>> authorityRules) throws ServiceException;

    void removeUserRelationship(long ownerId, long buddyId) throws ServiceException;

    void addUserRelationship(long ownerId, long buddyId, List<Long> nemosJoinedByBuddy) throws ServiceException;

    void deleteUserRelationshipRequest(long ownerId, long buddyId) throws ServiceException;

    void deleteUserRelationshipRequestByRequester(long requesterId, long requesterNemoId) throws ServiceException;

    void addFriendInvitation(long userId, String identifier, List<Long> nemos, Map<Long, List<AuthorityRule>> authorityRulesMap, RequestType requesterAppType) throws ServiceException;

    void acceptInvitation(long acceptUserId, long inviterUserId) throws ServiceException;

    void deleteFriendInvitationRequestByRequester(long requesterId, long requesterNemoId) throws ServiceException;

    List<FriendRequest> getFriendInvitation(long currentUserProfileId, RequestType requestType) throws ServiceException;

    List<UserProfilePO> getBuddyList(long userProfileId, FriendState friendState);

    List<FriendRequest> getFriendsByRequested(long userProfileId);

    List<UserDevicePO> getAllSeenNemosByUser(long userProfileId) throws ServiceException;

    List<UserDevicePO> getAllNemosManagedByUser(long userProfileId) throws ServiceException;

    List<UserDevicePO> getAllSeenNemosByNemo(long nemoId) throws ServiceException;

    @Deprecated
    List<UserDevicePO> getFriendDeviceList(long userProfileId, long friendProfileId) throws ServiceException;

    boolean isFriend(long userProfileId, long friendProfileId) throws ServiceException;

    @Deprecated
    List<UserDevicePO> getAuthorizedAndOwnDeviceList(long userProfileId) throws ServiceException;

    RequestVerificationCode sendActivationCode(String phone, Channel channel, SmsFunctionType smsFunctionType) throws ServiceException;

    RequestVerificationCode sendVerificationCodeForForgetPassword(String phone, Channel channel) throws ServiceException;

    RequestVerificationCode sendActivationCodeForLoginOrRegister(String phone, Channel channel) throws ServiceException;

    RequestVerificationCode sendVerificationCodeIgnoreCheckIdentifier(String mobileStr, Channel channel, String smsTemplate, String customizedKey, String sign, String smsUri, int expireTime, String application) throws ServiceException;

    UserProfilePO register(Register register, String customizedKey, boolean needVericationCode) throws ServiceException;

    Map<String, Object> weChatRegister(Register register, String customizedKey, SnAndFingerprint snAndFgp) throws ServiceException;

    UserProfilePO registerEnterpriseSuperAdmin(EnterpriseSuperAdminRegister register, boolean notCheckMail, TrackPoint trackPoint) throws ServiceException;

    UserProfilePO registerEnterpriseSuperAdminExistAll(UserProfilePO userProfilePO, EnterpriseSuperAdminRegister register, boolean notCheckMail, TrackPoint trackPoint) throws ServiceException;

    @Deprecated
    PrivilegeUsersAndPrivilegeNemos getUsersAndNemosPrivilegedFromNemo(long ownerUserId, long nemoId, Privilege[] privileges, Privilege[] excludePrivileges) throws ServiceException;

    @Deprecated
    void authorizePrivilege(long privilegingUserId, long privilegingNemoId, long privilegedUserId, long privilegedNemoId, Privilege privilege) throws ServiceException;

    @Deprecated
    void assignPrivilegesForBindedNemo(long ownerUserId, long nemoId) throws ServiceException;

    @Deprecated
    void abandonPrivilegesForUnbindedNemo(long ownerUserId, long nemoId) throws ServiceException;

    @Deprecated
    List<PrivilegingNemo> getNemosPrivilegingToUser(long privilegedUserId, Privilege[] privileges, Privilege[] excludePrivileges) throws ServiceException;

    @Deprecated
    List<PrivilegingNemo> getNemosPrivilegingToNemo(long privilegedNemoId, Privilege[] privileges, Privilege[] excludePrivileges) throws ServiceException;

    boolean userOwnNemo(long ownerUserId, long nemoId);

    /**
     * @param excluded
     * @return a list of UserProfilePO whose id in users and who is a friend of excluded
     */
    Set<UserProfilePO> mapToUserProfileExcludeOne(Set<Long> friendUserIds, long excluded) throws ServiceException;

    /**
     * Add user to one nemoCircle mangered by nemoOwner
     *
     * @param user
     * @param nemoOwner
     * @return
     */
    void userJoinCircleOfNemo(UserProfilePO user, List<UserDevicePO> nemosJoinedByUser,
                              UserProfilePO nemoOwner, Map<Long, List<AuthorityRule>> authorityRules) throws DataAccessException;

    boolean isUserInOneOrMoreCircle(long userid);

    boolean hasPrivacyPermission(long userId, long nemoId) throws ServiceException;

    void addRecordInBlacklist(String key, String account, int blockBindTime);

    void clearBinderrorRecord(String key);

    void checkBlacklist(DeviceOperationType type, String blockKey) throws ServiceException;

    List<Long> getNemosIdsSeenByUsers(long userProfileId1, long userProfileId2);

    public UserProfilePO getUserProfilePOByIdentityKey(String identityKey);


    // The following 5 methods maintains a black list of accounts. phone must be parseable, see Mobile
    List<BlackAccountPO> getBlackAccounts();

    boolean isABlackAccount(UserProfilePO userProfilePO);

    boolean isABlackAccount(String phone);

    boolean isABlackAccount(long userProfileId);

    /**
     * @param phone
     * @param reasongit
     * @return true means the phone is now in black list(maybe it is already in black list)
     */
    boolean addToBlacklist(String phone, String reason);

    boolean removeFromBlacklist(String phone);

    boolean addToBlacklist(long userProfileId, String reason);

    boolean removeFromBlacklist(long userProfileId);

    void addNewUserRelationship(long ownerId, long buddyId);

    void setUserPwd(String userId, String pwd, long passwordExpireTime, boolean isResetInitPwd, boolean checkEnhancePassword, boolean encryptedPassword, boolean sendSms);

    UserProfilePO getUserProfileByLoginId(String identifier, int userType);

    boolean existMailForUser(String mail);

    UserMailValidatePO findByMail(String mail);

    UserMailValidatePO findByToken(String token);

    void bindMail(String securityKey, String mail, String customizedkey);

    void update(UserMailValidatePO userMailValidatePO);

    void updateUserProfileExtPO(UserProfileExtPO userProfileExtPO);

    void putEmail(UserMailValidatePO userMailValidatePO);

    UserProfilePO getUserProfileByEmail(String email);

    UserProfilePO getUserProfileByEmailAllType(String email);

    UserMailValidatePO getByUserIdAndMaxValidateDate(String userId);

    boolean validateNewPassword(String password, String customizedKey);

    /**
     * 加密密码,密码规则后台校验
     *
     * <AUTHOR>
     * @date 2021-04-19
     */
    boolean validateEncryptNewPassword(String password, String customizedKey);

    boolean enableEnhancedPassword(String customizedKey);

    void updateUserPasswordExpireTime(UserPO userPO, String customizedKey);

    void bindMail(long userProfileId, String mail);

    long getPasswordExpireTime(String customizedkey, String enterpriseId, String distributorId);

    Boolean isValidUserProfileByEnterpriseId(Long userId);

    UserProfilePO wxAppRegister(Register register, String customizedKey, boolean needVericationCode, boolean sendPwdSms) throws ServiceException;

    void sendNewAccountPwdSms(String phone, String pwd, Channel channel, String enterpriseId);

    /**
     * 校验密码是否正确
     *
     * @param userProfileId
     * @param password
     * @param encryptedPassword
     * @return
     * @throws ServiceException when user not found
     */
    boolean verifyUserPassword(long userProfileId, String password, boolean encryptedPassword) throws ServiceException;

    /**
     * 修改用户手机号发送短信
     *
     * @param phone
     * @param channel
     * @return
     */
    RequestVerificationCode sendActivationCodeForUserPhoneChange(String phone, Channel channel);

    /**
     * 验证当前用户手机号(注册用户才可以使用)
     *
     * @param phone
     * @param channel
     * @param smsFunctionType
     * @return
     */
    RequestVerificationCode sendActivationCodeForUserPhoneVerify(String phone, Channel channel, SmsFunctionType smsFunctionType);

    /**
     * 设置用户密码的加密方式为  SHA512(SHA512(unEncryptedPassword)+salt)
     *
     * @param unEncryptedPassword
     * @param userId
     */
    void setUserPasswordBySha512_2(String unEncryptedPassword, String userId, String customizedKey);

    /**
     * 根据输入的用户标志获取到系统中记录标志
     * 转换：输入的不带国家码的手机号转换成带国家码
     *
     * @param inputIdentifier
     * @return
     */
    String convertIdentifier(String inputIdentifier) throws ServiceException;

    /**
     * 禁止注册期间的注册 需要创建临时账号对应，其他场景下不要使用
     *
     * @param register
     * @return
     * @throws ServiceException
     */
    UserProfilePO register(Register register) throws ServiceException;

    /**
     * 删除已验证邮箱的其他记录
     *
     * @param validateMail
     */
    void deleteAssociateMailValidate(UserMailValidatePO validateMail) throws DataAccessException;

    /**
     * 删除邮箱的未激活记录
     *
     * @param email
     */
    void deleteAssociateMailValidate(String email) throws DataAccessException;

    boolean isThirdUser(long userId);

    boolean isTempUser(long userId);

    UserProfilePO getUserProfilePOBySecurityKeyWithExpiration(String securityKey);

    UserProfilePO getUserProfileByPhone(String phone);

    int getUserCountByEnterpriseId(String enterpriseId);

    UserProfileExtPO getUserProfileExtPOByTelephone(String telephone);

    String generateAccountRandom(String account);

    UserProfilePO getUserProfilePOByMobileAndType(Mobile mobile, int type);

    void sendPasswordForReset(String phone, String password, Channel channel);
}
