package com.ainemo.libra.gamification.task.Definition;

import com.ainemo.libra.dataaccess.gamification.po.TaskRecordPO;
import com.ainemo.libra.gamification.task.JD081PlanTask;

/**
 * Created by <PERSON><PERSON><PERSON> on 16/7/13.
 */
public class JD081PlanTaskImpl extends BaseTaskImpl implements JD081PlanTask{

    @Override
    public void executeTask() {
        executeYear();
    }

    @Override
    public boolean isNodeFinish(TaskRecordPO record) {
        //判断完成的时间，这里先写死在为30分钟
        return record.getCallDuration() >= 60 * 60 * 1000;
    }

    @Override
    public boolean perposition(long userOwner, long nemoOwner) {

        return true;
    }
}
